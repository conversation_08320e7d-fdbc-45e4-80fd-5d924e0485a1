{"version": 3, "cmakeMinimumRequired": {"major": 3, "minor": 16}, "configurePresets": [{"name": "default", "hidden": false, "generator": "MinGW Makefiles", "description": "الإعداد الافتراضي لبناء مشروع HelloWorldApp باستخدام CMake و g++", "binaryDir": "${sourceDir}/build", "cacheVariables": {"CMAKE_BUILD_TYPE": "Debug", "CMAKE_EXPORT_COMPILE_COMMANDS": "YES"}}], "buildPresets": [{"name": "default-build", "configurePreset": "default", "description": "بناء باستخدام الإعداد الافتراضي", "jobs": 4}], "testPresets": []}