"C:\Users\<USER>\New folder (2)\cmake-4.0.3-windows-x86_64\bin\cmake.exe" -E rm -f CMakeFiles\HelloWorldApp.dir/objects.a
C:\Users\<USER>\mingw64\bin\ar.exe qc CMakeFiles\HelloWorldApp.dir/objects.a @CMakeFiles\HelloWorldApp.dir\objects1.rsp
C:\Users\<USER>\mingw64\bin\c++.exe -g -Wl,--whole-archive CMakeFiles\HelloWorldApp.dir/objects.a -Wl,--no-whole-archive -o HelloWorldApp.exe -Wl,--out-implib,libHelloWorldApp.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\HelloWorldApp.dir\linkLibs.rsp
