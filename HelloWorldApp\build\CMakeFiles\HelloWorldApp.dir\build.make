# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Users\<USER>\New folder (2)\cmake-4.0.3-windows-x86_64\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Users\<USER>\New folder (2)\cmake-4.0.3-windows-x86_64\bin\cmake.exe" -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = C:\3_calculator\HelloWorldApp

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = C:\3_calculator\HelloWorldApp\build

# Include any dependencies generated for this target.
include CMakeFiles/HelloWorldApp.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/HelloWorldApp.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/HelloWorldApp.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/HelloWorldApp.dir/flags.make

CMakeFiles/HelloWorldApp.dir/codegen:
.PHONY : CMakeFiles/HelloWorldApp.dir/codegen

CMakeFiles/HelloWorldApp.dir/main.cpp.obj: CMakeFiles/HelloWorldApp.dir/flags.make
CMakeFiles/HelloWorldApp.dir/main.cpp.obj: C:/3_calculator/HelloWorldApp/main.cpp
CMakeFiles/HelloWorldApp.dir/main.cpp.obj: CMakeFiles/HelloWorldApp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=C:\3_calculator\HelloWorldApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/HelloWorldApp.dir/main.cpp.obj"
	C:\Users\<USER>\mingw64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/HelloWorldApp.dir/main.cpp.obj -MF CMakeFiles\HelloWorldApp.dir\main.cpp.obj.d -o CMakeFiles\HelloWorldApp.dir\main.cpp.obj -c C:\3_calculator\HelloWorldApp\main.cpp

CMakeFiles/HelloWorldApp.dir/main.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/HelloWorldApp.dir/main.cpp.i"
	C:\Users\<USER>\mingw64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\3_calculator\HelloWorldApp\main.cpp > CMakeFiles\HelloWorldApp.dir\main.cpp.i

CMakeFiles/HelloWorldApp.dir/main.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/HelloWorldApp.dir/main.cpp.s"
	C:\Users\<USER>\mingw64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\3_calculator\HelloWorldApp\main.cpp -o CMakeFiles\HelloWorldApp.dir\main.cpp.s

# Object files for target HelloWorldApp
HelloWorldApp_OBJECTS = \
"CMakeFiles/HelloWorldApp.dir/main.cpp.obj"

# External object files for target HelloWorldApp
HelloWorldApp_EXTERNAL_OBJECTS =

HelloWorldApp.exe: CMakeFiles/HelloWorldApp.dir/main.cpp.obj
HelloWorldApp.exe: CMakeFiles/HelloWorldApp.dir/build.make
HelloWorldApp.exe: CMakeFiles/HelloWorldApp.dir/linkLibs.rsp
HelloWorldApp.exe: CMakeFiles/HelloWorldApp.dir/objects1.rsp
HelloWorldApp.exe: CMakeFiles/HelloWorldApp.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=C:\3_calculator\HelloWorldApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable HelloWorldApp.exe"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles\HelloWorldApp.dir\link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/HelloWorldApp.dir/build: HelloWorldApp.exe
.PHONY : CMakeFiles/HelloWorldApp.dir/build

CMakeFiles/HelloWorldApp.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles\HelloWorldApp.dir\cmake_clean.cmake
.PHONY : CMakeFiles/HelloWorldApp.dir/clean

CMakeFiles/HelloWorldApp.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" C:\3_calculator\HelloWorldApp C:\3_calculator\HelloWorldApp C:\3_calculator\HelloWorldApp\build C:\3_calculator\HelloWorldApp\build C:\3_calculator\HelloWorldApp\build\CMakeFiles\HelloWorldApp.dir\DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/HelloWorldApp.dir/depend

