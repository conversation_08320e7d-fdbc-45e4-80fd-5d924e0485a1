# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.0

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "MinGW Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "C:/3_calculator/HelloWorldApp/CMakeLists.txt"
  "CMakeFiles/4.0.3/CMakeCXXCompiler.cmake"
  "CMakeFiles/4.0.3/CMakeRCCompiler.cmake"
  "CMakeFiles/4.0.3/CMakeSystem.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeCXXCompiler.cmake.in"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeCXXCompilerABI.cpp"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeCXXInformation.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeCommonLanguageInclude.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeCompilerIdDetection.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeDetermineCXXCompiler.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeDetermineCompiler.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeDetermineCompilerSupport.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeDetermineRCCompiler.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeDetermineSystem.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeFindBinUtils.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeGenericSystem.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeInitializeConfigs.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeLanguageInformation.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeMinGWFindMake.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeParseImplicitIncludeInfo.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeParseImplicitLinkInfo.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeParseLibraryArchitecture.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeRCCompiler.cmake.in"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeRCInformation.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeSystem.cmake.in"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeSystemSpecificInformation.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeSystemSpecificInitialize.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeTestCompilerCommon.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/CMakeTestRCCompiler.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Compiler/ADSP-DetermineCompiler.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Compiler/ARMCC-DetermineCompiler.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Compiler/ARMClang-DetermineCompiler.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Compiler/AppleClang-DetermineCompiler.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Compiler/Borland-DetermineCompiler.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Compiler/CMakeCommonCompilerMacros.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Compiler/Clang-DetermineCompiler.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Compiler/Clang-DetermineCompilerInternal.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Compiler/Cray-DetermineCompiler.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Compiler/CrayClang-DetermineCompiler.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Compiler/Embarcadero-DetermineCompiler.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Compiler/Fujitsu-DetermineCompiler.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Compiler/GHS-DetermineCompiler.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Compiler/GNU-CXX.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Compiler/GNU-FindBinUtils.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Compiler/GNU.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Compiler/HP-CXX-DetermineCompiler.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Compiler/IAR-DetermineCompiler.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Compiler/IBMClang-CXX-DetermineCompiler.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Compiler/Intel-DetermineCompiler.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Compiler/LCC-CXX-DetermineCompiler.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Compiler/MSVC-DetermineCompiler.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Compiler/NVHPC-DetermineCompiler.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Compiler/NVIDIA-DetermineCompiler.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Compiler/OrangeC-DetermineCompiler.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Compiler/PGI-DetermineCompiler.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Compiler/PathScale-DetermineCompiler.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Compiler/SCO-DetermineCompiler.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Compiler/TI-DetermineCompiler.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Compiler/TIClang-DetermineCompiler.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Compiler/Tasking-DetermineCompiler.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Compiler/Watcom-DetermineCompiler.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Compiler/XL-CXX-DetermineCompiler.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Internal/CMakeCXXLinkerInformation.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Internal/CMakeCommonLinkerInformation.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Internal/CMakeDetermineLinkerId.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Internal/CMakeInspectCXXLinker.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Internal/FeatureTesting.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Platform/Linker/GNU.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Platform/Linker/Windows-CXX.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Platform/Linker/Windows-GNU-CXX.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Platform/Linker/Windows-GNU.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Platform/Windows-Determine-CXX.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Platform/Windows-GNU-CXX-ABI.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Platform/Windows-GNU-CXX.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Platform/Windows-GNU.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Platform/Windows-Initialize.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Platform/Windows-windres.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Platform/Windows.cmake"
  "C:/Users/<USER>/New folder (2)/cmake-4.0.3-windows-x86_64/share/cmake-4.0/Modules/Platform/WindowsPaths.cmake"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "CMakeFiles/4.0.3/CMakeSystem.cmake"
  "CMakeFiles/4.0.3/CMakeCXXCompiler.cmake"
  "CMakeFiles/4.0.3/CMakeRCCompiler.cmake"
  "CMakeFiles/4.0.3/CMakeCXXCompiler.cmake"
  "CMakeFiles/4.0.3/CMakeCXXCompiler.cmake"
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  "CMakeFiles/HelloWorldApp.dir/DependInfo.cmake"
  )
